/**
 * Admin authentication hook - uses shared logic with 'admin' role
 */
import { useAuth as useSharedAuth, getFullName, getUserInitials } from '@grandline/auth-core';

// Re-export types
export type { AuthUser, AuthState } from '@grandline/auth-core';

/**
 * Admin-specific authentication hook
 */
export function useAuth() {
  return useSharedAuth('admin'); // Only difference: role = 'admin'
}

// Re-export utility functions
export { getFullName, getUserInitials };
