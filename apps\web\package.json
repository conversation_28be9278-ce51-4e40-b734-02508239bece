{"name": "@encreasl/web", "version": "0.1.0", "private": true, "scripts": {"build": "node ../../node_modules/next/dist/bin/next build", "dev": "node ../../node_modules/next/dist/bin/next dev", "dev:turbo": "node ../../node_modules/next/dist/bin/next dev --turbopack", "lint": "node ../../node_modules/next/dist/bin/next lint", "start": "node ../../node_modules/next/dist/bin/next start", "type-check": "tsc --noEmit"}, "dependencies": {"@encreasl/redux": "workspace:*", "@grandline/auth-core": "workspace:*", "@grandline/auth-ui": "workspace:*", "next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "zod": "^3.25.76"}, "devDependencies": {"@encreasl/env": "workspace:*", "@encreasl/eslint-config": "workspace:*", "@encreasl/typescript-config": "workspace:*", "@encreasl/ui": "workspace:*", "@eslint/js": "^9", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8", "@typescript-eslint/parser": "^8", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "typescript": "^5"}}