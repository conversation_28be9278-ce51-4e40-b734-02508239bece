{"version": 3, "sources": ["../src/index.ts", "../src/hooks/useAuth.ts", "../src/utils/auth-api.ts", "../src/utils/validation.ts", "../src/middleware/auth-middleware.ts", "../src/config/auth-presets.ts"], "sourcesContent": ["/**\n * @grandline/auth-core\n * \n * Shared authentication core logic for Grandline monorepo\n * Provides configurable authentication hooks, middleware, and utilities\n * for PayloadCMS-based authentication across all apps.\n */\n\n// Types - simplified\nexport type {\n  AuthUser,\n  AuthState,\n} from './hooks/useAuth';\n\n// Hooks\nexport { useAuth, getFullName, getUserInitials } from './hooks/useAuth';\n\n// API Utilities\nexport { PayloadCMSAuth } from './utils/auth-api';\nexport type { LoginCredentials, LoginResponse } from './utils/auth-api';\n\n// Validation Utilities\nexport { AuthValidator, ValidationUtils } from './utils/validation';\n\n// Middleware\nexport {\n  createAuthMiddleware,\n  createAdminAuthMiddleware,\n  createTraineeAuthMiddleware,\n  createInstructorAuthMiddleware,\n  createMultiRoleAuthMiddleware,\n} from './middleware/auth-middleware';\n\n// Configuration Presets\nexport {\n  defaultSecurityConfig,\n  highSecurityConfig,\n  standardSecurityConfig,\n  createAdminAuthConfig,\n  createTraineeAuthConfig,\n  createInstructorAuthConfig,\n  createMultiRoleAuthConfig,\n  DEFAULT_API_URL,\n  AuthPresets,\n} from './config/auth-presets';\n", "/**\n * Simple authentication hook - EXACT copy from apps/web-admin\n * Only configurable parameter: allowedRole\n */\n\nimport { useState, useEffect } from 'react';\n\nexport interface AuthUser {\n  id: number;\n  email: string;\n  firstName: string;\n  lastName: string;\n  middleName?: string;\n  username?: string;\n  role: string;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface AuthState {\n  user: AuthUser | null;\n  loading: boolean;\n  error: string | null;\n  isAuthenticated: boolean;\n  securityAlert: {\n    show: boolean;\n    type: 'role-changed' | 'account-deactivated' | 'session-expired';\n    message: string;\n  } | null;\n}\n\n/**\n * Simple authentication hook with configurable role\n * EXACT same logic as apps/web-admin, just different role\n */\nexport function useAuth(allowedRole: string): AuthState {\n  const [user, setUser] = useState<AuthUser | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [securityAlert, setSecurityAlert] = useState<AuthState['securityAlert']>(null);\n\n  useEffect(() => {\n    async function fetchCurrentUser() {\n      try {\n        const apiUrl = 'https://grandline-cms.vercel.app/api';\n\n        // Get the payload token from cookies\n        const payloadToken = document.cookie\n          .split('; ')\n          .find(row => row.startsWith('payload-token='))\n          ?.split('=')[1];\n\n        console.log('🔍 useAuth: Looking for payload-token in cookies');\n        console.log('🍪 useAuth: All cookies:', document.cookie);\n        console.log('🎫 useAuth: Found token:', payloadToken ? 'Yes' : 'No');\n\n        const response = await fetch(`${apiUrl}/users/me`, {\n          credentials: 'include',\n          headers: {\n            'Content-Type': 'application/json',\n            ...(payloadToken && { 'Authorization': `Bearer ${payloadToken}` })\n          }\n        });\n\n        console.log('📡 useAuth: API response status:', response.status);\n\n        if (response.ok) {\n          const userData = await response.json();\n          console.log('📦 useAuth: API response data:', userData);\n\n          // Handle PayloadCMS complex response structure\n          let extractedUser: any = null;\n          if (userData.user) {\n            // Structure: { user: {...}, message: \"Account\", token: \"...\" }\n            extractedUser = userData.user;\n            console.log('✅ useAuth: Extracted user from userData.user');\n          } else if (userData.id && userData.email) {\n            // Structure: { id, email, firstName, ... }\n            extractedUser = userData;\n            console.log('✅ useAuth: Extracted user from userData directly');\n          } else {\n            console.error('❌ useAuth: Could not extract user from response structure');\n          }\n\n          if (extractedUser) {\n            // SECURITY CHECK: Validate current role in real-time\n            if (extractedUser.role !== allowedRole) {\n              console.error('🚨 SECURITY ALERT: User role changed, logging out');\n              console.error('Current role:', extractedUser.role);\n              console.error('Required role:', allowedRole);\n\n              // Show security alert\n              setSecurityAlert({\n                show: true,\n                type: 'role-changed',\n                message: `Your role has been changed from ${allowedRole} to ${extractedUser.role}. You no longer have access to this application.`\n              });\n\n              // Clear authentication\n              document.cookie.split(\";\").forEach(function(c) {\n                document.cookie = c.replace(/^ +/, \"\").replace(/=.*/, \"=;expires=\" + new Date().toUTCString() + \";path=/\");\n              });\n\n              setError(`Access denied. ${allowedRole} role required. Current role: ${extractedUser.role}`);\n              setUser(null);\n\n              return;\n            }\n\n            // SECURITY CHECK: Validate account is still active\n            if (!extractedUser.isActive) {\n              console.error('🚨 SECURITY ALERT: User account deactivated, logging out');\n\n              // Show security alert\n              setSecurityAlert({\n                show: true,\n                type: 'account-deactivated',\n                message: 'Your account has been deactivated by an administrator. Please contact support for assistance.'\n              });\n\n              // Clear authentication\n              document.cookie.split(\";\").forEach(function(c) {\n                document.cookie = c.replace(/^ +/, \"\").replace(/=.*/, \"=;expires=\" + new Date().toUTCString() + \";path=/\");\n              });\n\n              setError('Account has been deactivated. Please contact administrator.');\n              setUser(null);\n\n              return;\n            }\n\n            setUser(extractedUser);\n            setError(null);\n          } else {\n            setError('Unable to extract user data from response');\n          }\n        } else {\n          const errorText = await response.text();\n          setError(`Authentication failed: ${response.status} ${errorText}`);\n        }\n      } catch (err) {\n        setError(`Network error: ${err}`);\n      } finally {\n        setLoading(false);\n      }\n    }\n\n    fetchCurrentUser();\n\n    // Set up periodic role validation (every 30 seconds)\n    const roleValidationInterval = setInterval(() => {\n      if (user) {\n        console.log('🔍 Performing periodic role validation...');\n        fetchCurrentUser();\n      }\n    }, 30000); // Check every 30 seconds\n\n    return () => {\n      clearInterval(roleValidationInterval);\n    };\n  }, [user, allowedRole]);\n\n  return {\n    user,\n    loading,\n    error,\n    isAuthenticated: !!user && !error,\n    securityAlert\n  };\n}\n\n/**\n * Get user's full name - EXACT same as apps/web-admin\n */\nexport function getFullName(user: AuthUser | null): string {\n  if (!user) return 'User';\n\n  const firstName = user.firstName || '';\n  const lastName = user.lastName || '';\n\n  if (firstName && lastName) {\n    return `${firstName} ${lastName}`;\n  } else if (firstName) {\n    return firstName;\n  } else if (lastName) {\n    return lastName;\n  } else {\n    return user.email?.split('@')[0] || 'User';\n  }\n}\n\n/**\n * Get user's initials - EXACT same as apps/web-admin\n */\nexport function getUserInitials(user: AuthUser | null): string {\n  if (!user) return 'U';\n\n  const firstName = user.firstName || '';\n  const lastName = user.lastName || '';\n\n  if (firstName && lastName) {\n    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();\n  } else if (firstName) {\n    return firstName.charAt(0).toUpperCase();\n  } else if (lastName) {\n    return lastName.charAt(0).toUpperCase();\n  } else {\n    return user.email?.charAt(0).toUpperCase() || 'U';\n  }\n}\n", "/**\n * PayloadCMS Authentication API utilities\n * Extracted from apps/web-admin/src/hooks/useAuth.ts\n */\n\nimport type { AuthUser } from '../types/auth';\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  user: AuthUser;\n  token?: string;\n  message?: string;\n}\n\nexport class PayloadCMSAuth {\n  private apiUrl: string;\n  private cookieName: string;\n\n  constructor(apiUrl: string, cookieName: string = 'payload-token') {\n    this.apiUrl = apiUrl;\n    this.cookieName = cookieName;\n  }\n\n  /**\n   * Login user with email and password\n   */\n  async login(credentials: LoginCredentials): Promise<LoginResponse> {\n    const response = await fetch(`${this.apiUrl}/users/login`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(credentials),\n      credentials: 'include', // Important for cookie handling\n    });\n\n    if (!response.ok) {\n      const result = await response.json();\n      throw new Error(result.message || 'Login failed');\n    }\n\n    const result = await response.json();\n\n    // If we have a custom cookie name (not the default), we need to copy the cookie\n    if (this.cookieName !== 'payload-token' && typeof document !== 'undefined') {\n      console.log('🔄 Setting up custom cookie:', this.cookieName);\n\n      // Get the PayloadCMS token that was just set\n      const payloadToken = document.cookie\n        .split('; ')\n        .find(row => row.startsWith('payload-token='))\n        ?.split('=')[1];\n\n      console.log('🍪 PayloadCMS token found:', payloadToken ? 'Yes' : 'No');\n\n      if (payloadToken) {\n        // Set our custom cookie with the same token\n        const isSecure = typeof window !== 'undefined' && window.location.protocol === 'https:';\n        const cookieString = `${this.cookieName}=${payloadToken}; path=/; SameSite=Lax${isSecure ? '; Secure' : ''}`;\n        document.cookie = cookieString;\n        console.log('✅ Custom cookie set:', cookieString);\n\n        // Clear the default PayloadCMS cookie to avoid conflicts\n        document.cookie = 'payload-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';\n        console.log('🗑️ Default PayloadCMS cookie cleared');\n\n        // Verify the cookie was set\n        const verification = document.cookie\n          .split('; ')\n          .find(row => row.startsWith(`${this.cookieName}=`));\n        console.log('🔍 Cookie verification:', verification ? 'Success' : 'Failed');\n      } else {\n        console.warn('⚠️ No PayloadCMS token found to copy');\n      }\n    }\n\n    return result;\n  }\n\n  /**\n   * Get current authenticated user\n   */\n  async getCurrentUser(): Promise<AuthUser | null> {\n    try {\n      // Get the payload token from cookies\n      const payloadToken = this.getPayloadToken();\n\n      const response = await fetch(`${this.apiUrl}/users/me`, {\n        credentials: 'include',\n        headers: {\n          'Content-Type': 'application/json',\n          'Cookie': payloadToken ? `payload-token=${payloadToken}` : '',\n          ...(payloadToken && { 'Authorization': `Bearer ${payloadToken}` })\n        }\n      });\n\n      if (!response.ok) {\n        return null;\n      }\n\n      const userData = await response.json();\n      \n      // Handle PayloadCMS complex response structure\n      let extractedUser: any = null;\n      if (userData.user) {\n        // Structure: { user: {...}, message: \"Account\", token: \"...\" }\n        extractedUser = userData.user;\n      } else if (userData.id && userData.email) {\n        // Structure: { id, email, firstName, ... }\n        extractedUser = userData;\n      }\n\n      return extractedUser;\n    } catch (error) {\n      console.error('Error fetching current user:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  async logout(): Promise<void> {\n    try {\n      await fetch(`${this.apiUrl}/users/logout`, {\n        method: 'POST',\n        credentials: 'include',\n        headers: {\n          'Content-Type': 'application/json',\n        }\n      });\n    } catch (error) {\n      console.error('Error during logout:', error);\n    } finally {\n      // Clear cookies regardless of API response\n      this.clearAuthCookies();\n    }\n  }\n\n  /**\n   * Get PayloadCMS token from cookies\n   */\n  private getPayloadToken(): string | undefined {\n    if (typeof document === 'undefined') return undefined;\n\n    return document.cookie\n      .split('; ')\n      .find(row => row.startsWith(`${this.cookieName}=`))\n      ?.split('=')[1];\n  }\n\n  /**\n   * Clear authentication cookies\n   */\n  clearAuthCookies(): void {\n    if (typeof document === 'undefined') return;\n\n    // Clear our app-specific cookie\n    document.cookie = `${this.cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\n\n    // Also clear the default PayloadCMS cookie if it exists\n    document.cookie = 'payload-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';\n  }\n}\n", "/**\n * Authentication validation utilities\n */\n\nimport type { AuthUser, RoleValidationResult, SecurityAlert } from '../types/auth';\n\nexport class AuthValidator {\n  /**\n   * Validate if user has required role\n   */\n  static validateUserRole(user: AuthUser | null, allowedRoles: string[]): RoleValidationResult {\n    if (!user) {\n      return {\n        isValid: false,\n        expectedRoles: allowedRoles,\n        reason: 'User not authenticated'\n      };\n    }\n\n    if (!allowedRoles.includes(user.role)) {\n      return {\n        isValid: false,\n        currentRole: user.role,\n        expectedRoles: allowedRoles,\n        reason: `User role '${user.role}' not in allowed roles: ${allowedRoles.join(', ')}`\n      };\n    }\n\n    return {\n      isValid: true,\n      currentRole: user.role,\n      expectedRoles: allowedRoles\n    };\n  }\n\n  /**\n   * Validate if user account is active\n   */\n  static validateUserActive(user: AuthUser | null): { isValid: boolean; reason?: string } {\n    if (!user) {\n      return {\n        isValid: false,\n        reason: 'User not authenticated'\n      };\n    }\n\n    if (!user.isActive) {\n      return {\n        isValid: false,\n        reason: 'User account is deactivated'\n      };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Generate security alert for role change\n   */\n  static createRoleChangeAlert(currentRole: string, allowedRoles: string[]): SecurityAlert {\n    return {\n      show: true,\n      type: 'role-changed',\n      message: `Your role has been changed from ${allowedRoles.join('/')} to ${currentRole}. You no longer have access to this application.`\n    };\n  }\n\n  /**\n   * Generate security alert for account deactivation\n   */\n  static createAccountDeactivatedAlert(): SecurityAlert {\n    return {\n      show: true,\n      type: 'account-deactivated',\n      message: 'Your account has been deactivated by an administrator. Please contact support for assistance.'\n    };\n  }\n\n  /**\n   * Generate security alert for session expiration\n   */\n  static createSessionExpiredAlert(): SecurityAlert {\n    return {\n      show: true,\n      type: 'session-expired',\n      message: 'Your session has expired. Please log in again to continue.'\n    };\n  }\n}\n\n/**\n * Utility functions for common validation patterns\n */\nexport const ValidationUtils = {\n  /**\n   * Check if user is admin\n   */\n  isAdmin: (user: AuthUser | null): boolean => {\n    return AuthValidator.validateUserRole(user, ['admin']).isValid;\n  },\n\n  /**\n   * Check if user is trainee\n   */\n  isTrainee: (user: AuthUser | null): boolean => {\n    return AuthValidator.validateUserRole(user, ['trainee']).isValid;\n  },\n\n  /**\n   * Check if user is instructor\n   */\n  isInstructor: (user: AuthUser | null): boolean => {\n    return AuthValidator.validateUserRole(user, ['instructor']).isValid;\n  },\n\n  /**\n   * Check if user has any of the specified roles\n   */\n  hasAnyRole: (user: AuthUser | null, roles: string[]): boolean => {\n    return AuthValidator.validateUserRole(user, roles).isValid;\n  }\n};\n", "/**\n * Configurable authentication middleware for Next.js\n * Extracted and enhanced from apps/web-admin/src/middleware.ts\n */\n\nimport { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\nimport type { AuthMiddlewareConfig } from '../types/auth';\n\n/**\n * Create configurable authentication middleware\n */\nexport function createAuthMiddleware(config: AuthMiddlewareConfig) {\n  return async function authMiddleware(request: NextRequest) {\n    const { pathname } = request.nextUrl;\n\n    // Allow access to public paths\n    if (config.publicPaths.some(path => pathname.startsWith(path))) {\n      return NextResponse.next();\n    }\n\n    // Check for PayloadCMS authentication cookie\n    const payloadToken = request.cookies.get(config.cookieName);\n\n    if (!payloadToken) {\n      console.log('❌ No auth cookie found, redirecting to login');\n      return NextResponse.redirect(new URL(config.loginPath, request.url));\n    }\n\n    // 🛡️ SECURITY ENHANCEMENT: Real-time role validation\n    try {\n      const response = await fetch(`${config.apiUrl}/users/me`, {\n        headers: {\n          'Cookie': `${config.cookieName}=${payloadToken.value}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const userData = await response.json();\n        const user = userData.user || userData;\n\n        // 🚨 Check if user role is in allowed roles\n        if (user && !config.allowedRoles.includes(user.role)) {\n          console.log('🚨 MIDDLEWARE SECURITY: User role not allowed, blocking access');\n          console.log('Current role:', user.role);\n          console.log('Allowed roles:', config.allowedRoles);\n          \n          // Clear the invalid cookie and redirect to login\n          const response = NextResponse.redirect(new URL(config.loginPath, request.url));\n          response.cookies.delete(config.cookieName);\n          return response;\n        }\n\n        // 🚨 Check if user account is still active\n        if (user && !user.isActive) {\n          console.log('🚨 MIDDLEWARE SECURITY: User account deactivated, blocking access');\n          \n          // Clear the invalid cookie and redirect to login\n          const response = NextResponse.redirect(new URL(config.loginPath, request.url));\n          response.cookies.delete(config.cookieName);\n          return response;\n        }\n\n        console.log('✅ Auth cookie and role validated, allowing access');\n        return NextResponse.next();\n      } else {\n        console.log('❌ Token validation failed, redirecting to login');\n        const response = NextResponse.redirect(new URL(config.loginPath, request.url));\n        response.cookies.delete(config.cookieName);\n        return response;\n      }\n    } catch (error) {\n      console.error('❌ Error validating token:', error);\n      // On error, allow access but log the issue (fallback to client-side validation)\n      console.log('⚠️ Falling back to client-side validation');\n      return NextResponse.next();\n    }\n  };\n}\n\n/**\n * Pre-configured middleware for admin authentication\n */\nexport function createAdminAuthMiddleware(apiUrl: string) {\n  return createAuthMiddleware({\n    apiUrl,\n    allowedRoles: ['admin'],\n    loginPath: '/admin/login',\n    cookieName: 'admin-auth-token', // Admin-specific cookie\n    publicPaths: ['/admin/login']\n  });\n}\n\n/**\n * Pre-configured middleware for trainee authentication\n */\nexport function createTraineeAuthMiddleware(apiUrl: string) {\n  return createAuthMiddleware({\n    apiUrl,\n    allowedRoles: ['trainee'],\n    loginPath: '/login',\n    cookieName: 'trainee-auth-token', // Trainee-specific cookie\n    publicPaths: ['/login', '/register', '/signin']\n  });\n}\n\n/**\n * Pre-configured middleware for instructor authentication\n */\nexport function createInstructorAuthMiddleware(apiUrl: string) {\n  return createAuthMiddleware({\n    apiUrl,\n    allowedRoles: ['instructor'],\n    loginPath: '/instructor/login',\n    cookieName: 'instructor-auth-token', // Instructor-specific cookie\n    publicPaths: ['/instructor/login']\n  });\n}\n\n/**\n * Multi-role middleware (for apps that allow multiple roles)\n */\nexport function createMultiRoleAuthMiddleware(\n  apiUrl: string,\n  allowedRoles: string[],\n  loginPath: string,\n  cookieName: string,\n  publicPaths: string[] = []\n) {\n  return createAuthMiddleware({\n    apiUrl,\n    allowedRoles,\n    loginPath,\n    cookieName,\n    publicPaths: [...publicPaths, loginPath]\n  });\n}\n", "/**\n * Pre-configured authentication configurations for different app types\n */\n\nimport type { AuthConfig, SecurityConfig } from '../types/auth';\n\n/**\n * Default security configuration\n */\nexport const defaultSecurityConfig: SecurityConfig = {\n  periodicValidation: 30000,        // 30 seconds\n  showSecurityAlerts: true,\n  autoLogoutOnRoleChange: true,\n  autoLogoutOnDeactivation: true,\n  alertRedirectDelay: 5000,         // 5 seconds\n};\n\n/**\n * High-security configuration (for admin apps)\n */\nexport const highSecurityConfig: SecurityConfig = {\n  periodicValidation: 30000,        // 30 seconds - frequent validation\n  showSecurityAlerts: true,\n  autoLogoutOnRoleChange: true,\n  autoLogoutOnDeactivation: true,\n  alertRedirectDelay: 3000,         // 3 seconds - faster redirect\n};\n\n/**\n * Standard security configuration (for user apps)\n */\nexport const standardSecurityConfig: SecurityConfig = {\n  periodicValidation: 60000,        // 60 seconds - less frequent validation\n  showSecurityAlerts: true,\n  autoLogoutOnRoleChange: true,\n  autoLogoutOnDeactivation: true,\n  alertRedirectDelay: 5000,         // 5 seconds\n};\n\n/**\n * Create admin authentication configuration\n */\nexport function createAdminAuthConfig(apiUrl: string): AuthConfig {\n  return {\n    apiUrl,\n    allowedRoles: ['admin'],\n    loginPath: '/admin/login',\n    dashboardPath: '/admin/dashboard',\n    cookieName: 'admin-auth-token', // Admin-specific cookie\n    securityConfig: highSecurityConfig,\n  };\n}\n\n/**\n * Create trainee authentication configuration\n */\nexport function createTraineeAuthConfig(apiUrl: string): AuthConfig {\n  return {\n    apiUrl,\n    allowedRoles: ['trainee'],\n    loginPath: '/login',\n    dashboardPath: '/dashboard',\n    cookieName: 'trainee-auth-token', // Trainee-specific cookie\n    securityConfig: standardSecurityConfig,\n  };\n}\n\n/**\n * Create instructor authentication configuration\n */\nexport function createInstructorAuthConfig(apiUrl: string): AuthConfig {\n  return {\n    apiUrl,\n    allowedRoles: ['instructor'],\n    loginPath: '/instructor/login',\n    dashboardPath: '/instructor/dashboard',\n    cookieName: 'instructor-auth-token', // Instructor-specific cookie\n    securityConfig: standardSecurityConfig,\n  };\n}\n\n/**\n * Create multi-role authentication configuration\n */\nexport function createMultiRoleAuthConfig(\n  apiUrl: string,\n  allowedRoles: string[],\n  loginPath: string,\n  dashboardPath: string,\n  cookieName: string,\n  securityConfig: SecurityConfig = defaultSecurityConfig\n): AuthConfig {\n  return {\n    apiUrl,\n    allowedRoles,\n    loginPath,\n    dashboardPath,\n    cookieName,\n    securityConfig,\n  };\n}\n\n/**\n * Default API URL for PayloadCMS\n */\nexport const DEFAULT_API_URL = 'https://grandline-cms.vercel.app/api';\n\n/**\n * Pre-configured auth configs using default API URL\n */\nexport const AuthPresets = {\n  admin: createAdminAuthConfig(DEFAULT_API_URL),\n  trainee: createTraineeAuthConfig(DEFAULT_API_URL),\n  instructor: createInstructorAuthConfig(DEFAULT_API_URL),\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACKA,mBAAoC;AA+B7B,SAAS,QAAQ,aAAgC;AACtD,QAAM,CAAC,MAAM,OAAO,QAAI,uBAA0B,IAAI;AACtD,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,IAAI;AAC3C,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAwB,IAAI;AACtD,QAAM,CAAC,eAAe,gBAAgB,QAAI,uBAAqC,IAAI;AAEnF,8BAAU,MAAM;AACd,mBAAe,mBAAmB;AAChC,UAAI;AACF,cAAM,SAAS;AAGf,cAAM,eAAe,SAAS,OAC3B,MAAM,IAAI,EACV,KAAK,SAAO,IAAI,WAAW,gBAAgB,CAAC,GAC3C,MAAM,GAAG,EAAE,CAAC;AAEhB,gBAAQ,IAAI,yDAAkD;AAC9D,gBAAQ,IAAI,mCAA4B,SAAS,MAAM;AACvD,gBAAQ,IAAI,mCAA4B,eAAe,QAAQ,IAAI;AAEnE,cAAM,WAAW,MAAM,MAAM,GAAG,MAAM,aAAa;AAAA,UACjD,aAAa;AAAA,UACb,SAAS;AAAA,YACP,gBAAgB;AAAA,YAChB,GAAI,gBAAgB,EAAE,iBAAiB,UAAU,YAAY,GAAG;AAAA,UAClE;AAAA,QACF,CAAC;AAED,gBAAQ,IAAI,2CAAoC,SAAS,MAAM;AAE/D,YAAI,SAAS,IAAI;AACf,gBAAM,WAAW,MAAM,SAAS,KAAK;AACrC,kBAAQ,IAAI,yCAAkC,QAAQ;AAGtD,cAAI,gBAAqB;AACzB,cAAI,SAAS,MAAM;AAEjB,4BAAgB,SAAS;AACzB,oBAAQ,IAAI,mDAA8C;AAAA,UAC5D,WAAW,SAAS,MAAM,SAAS,OAAO;AAExC,4BAAgB;AAChB,oBAAQ,IAAI,uDAAkD;AAAA,UAChE,OAAO;AACL,oBAAQ,MAAM,gEAA2D;AAAA,UAC3E;AAEA,cAAI,eAAe;AAEjB,gBAAI,cAAc,SAAS,aAAa;AACtC,sBAAQ,MAAM,0DAAmD;AACjE,sBAAQ,MAAM,iBAAiB,cAAc,IAAI;AACjD,sBAAQ,MAAM,kBAAkB,WAAW;AAG3C,+BAAiB;AAAA,gBACf,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS,mCAAmC,WAAW,OAAO,cAAc,IAAI;AAAA,cAClF,CAAC;AAGD,uBAAS,OAAO,MAAM,GAAG,EAAE,QAAQ,SAAS,GAAG;AAC7C,yBAAS,SAAS,EAAE,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,gBAAe,oBAAI,KAAK,GAAE,YAAY,IAAI,SAAS;AAAA,cAC3G,CAAC;AAED,uBAAS,kBAAkB,WAAW,iCAAiC,cAAc,IAAI,EAAE;AAC3F,sBAAQ,IAAI;AAEZ;AAAA,YACF;AAGA,gBAAI,CAAC,cAAc,UAAU;AAC3B,sBAAQ,MAAM,iEAA0D;AAGxE,+BAAiB;AAAA,gBACf,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,cACX,CAAC;AAGD,uBAAS,OAAO,MAAM,GAAG,EAAE,QAAQ,SAAS,GAAG;AAC7C,yBAAS,SAAS,EAAE,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,gBAAe,oBAAI,KAAK,GAAE,YAAY,IAAI,SAAS;AAAA,cAC3G,CAAC;AAED,uBAAS,6DAA6D;AACtE,sBAAQ,IAAI;AAEZ;AAAA,YACF;AAEA,oBAAQ,aAAa;AACrB,qBAAS,IAAI;AAAA,UACf,OAAO;AACL,qBAAS,2CAA2C;AAAA,UACtD;AAAA,QACF,OAAO;AACL,gBAAM,YAAY,MAAM,SAAS,KAAK;AACtC,mBAAS,0BAA0B,SAAS,MAAM,IAAI,SAAS,EAAE;AAAA,QACnE;AAAA,MACF,SAAS,KAAK;AACZ,iBAAS,kBAAkB,GAAG,EAAE;AAAA,MAClC,UAAE;AACA,mBAAW,KAAK;AAAA,MAClB;AAAA,IACF;AAEA,qBAAiB;AAGjB,UAAM,yBAAyB,YAAY,MAAM;AAC/C,UAAI,MAAM;AACR,gBAAQ,IAAI,kDAA2C;AACvD,yBAAiB;AAAA,MACnB;AAAA,IACF,GAAG,GAAK;AAER,WAAO,MAAM;AACX,oBAAc,sBAAsB;AAAA,IACtC;AAAA,EACF,GAAG,CAAC,MAAM,WAAW,CAAC;AAEtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB,CAAC,CAAC,QAAQ,CAAC;AAAA,IAC5B;AAAA,EACF;AACF;AAKO,SAAS,YAAY,MAA+B;AACzD,MAAI,CAAC,KAAM,QAAO;AAElB,QAAM,YAAY,KAAK,aAAa;AACpC,QAAM,WAAW,KAAK,YAAY;AAElC,MAAI,aAAa,UAAU;AACzB,WAAO,GAAG,SAAS,IAAI,QAAQ;AAAA,EACjC,WAAW,WAAW;AACpB,WAAO;AAAA,EACT,WAAW,UAAU;AACnB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,KAAK,OAAO,MAAM,GAAG,EAAE,CAAC,KAAK;AAAA,EACtC;AACF;AAKO,SAAS,gBAAgB,MAA+B;AAC7D,MAAI,CAAC,KAAM,QAAO;AAElB,QAAM,YAAY,KAAK,aAAa;AACpC,QAAM,WAAW,KAAK,YAAY;AAElC,MAAI,aAAa,UAAU;AACzB,YAAQ,UAAU,OAAO,CAAC,IAAI,SAAS,OAAO,CAAC,GAAG,YAAY;AAAA,EAChE,WAAW,WAAW;AACpB,WAAO,UAAU,OAAO,CAAC,EAAE,YAAY;AAAA,EACzC,WAAW,UAAU;AACnB,WAAO,SAAS,OAAO,CAAC,EAAE,YAAY;AAAA,EACxC,OAAO;AACL,WAAO,KAAK,OAAO,OAAO,CAAC,EAAE,YAAY,KAAK;AAAA,EAChD;AACF;;;AChMO,IAAM,iBAAN,MAAqB;AAAA,EAI1B,YAAY,QAAgB,aAAqB,iBAAiB;AAChE,SAAK,SAAS;AACd,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,MAAM,aAAuD;AACjE,UAAM,WAAW,MAAM,MAAM,GAAG,KAAK,MAAM,gBAAgB;AAAA,MACzD,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,MACA,MAAM,KAAK,UAAU,WAAW;AAAA,MAChC,aAAa;AAAA;AAAA,IACf,CAAC;AAED,QAAI,CAAC,SAAS,IAAI;AAChB,YAAMA,UAAS,MAAM,SAAS,KAAK;AACnC,YAAM,IAAI,MAAMA,QAAO,WAAW,cAAc;AAAA,IAClD;AAEA,UAAM,SAAS,MAAM,SAAS,KAAK;AAGnC,QAAI,KAAK,eAAe,mBAAmB,OAAO,aAAa,aAAa;AAC1E,cAAQ,IAAI,uCAAgC,KAAK,UAAU;AAG3D,YAAM,eAAe,SAAS,OAC3B,MAAM,IAAI,EACV,KAAK,SAAO,IAAI,WAAW,gBAAgB,CAAC,GAC3C,MAAM,GAAG,EAAE,CAAC;AAEhB,cAAQ,IAAI,qCAA8B,eAAe,QAAQ,IAAI;AAErE,UAAI,cAAc;AAEhB,cAAM,WAAW,OAAO,WAAW,eAAe,OAAO,SAAS,aAAa;AAC/E,cAAM,eAAe,GAAG,KAAK,UAAU,IAAI,YAAY,yBAAyB,WAAW,aAAa,EAAE;AAC1G,iBAAS,SAAS;AAClB,gBAAQ,IAAI,6BAAwB,YAAY;AAGhD,iBAAS,SAAS;AAClB,gBAAQ,IAAI,mDAAuC;AAGnD,cAAM,eAAe,SAAS,OAC3B,MAAM,IAAI,EACV,KAAK,SAAO,IAAI,WAAW,GAAG,KAAK,UAAU,GAAG,CAAC;AACpD,gBAAQ,IAAI,kCAA2B,eAAe,YAAY,QAAQ;AAAA,MAC5E,OAAO;AACL,gBAAQ,KAAK,gDAAsC;AAAA,MACrD;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBAA2C;AAC/C,QAAI;AAEF,YAAM,eAAe,KAAK,gBAAgB;AAE1C,YAAM,WAAW,MAAM,MAAM,GAAG,KAAK,MAAM,aAAa;AAAA,QACtD,aAAa;AAAA,QACb,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,UAAU,eAAe,iBAAiB,YAAY,KAAK;AAAA,UAC3D,GAAI,gBAAgB,EAAE,iBAAiB,UAAU,YAAY,GAAG;AAAA,QAClE;AAAA,MACF,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,eAAO;AAAA,MACT;AAEA,YAAM,WAAW,MAAM,SAAS,KAAK;AAGrC,UAAI,gBAAqB;AACzB,UAAI,SAAS,MAAM;AAEjB,wBAAgB,SAAS;AAAA,MAC3B,WAAW,SAAS,MAAM,SAAS,OAAO;AAExC,wBAAgB;AAAA,MAClB;AAEA,aAAO;AAAA,IACT,SAAS,OAAO;AACd,cAAQ,MAAM,gCAAgC,KAAK;AACnD,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,SAAwB;AAC5B,QAAI;AACF,YAAM,MAAM,GAAG,KAAK,MAAM,iBAAiB;AAAA,QACzC,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH,SAAS,OAAO;AACd,cAAQ,MAAM,wBAAwB,KAAK;AAAA,IAC7C,UAAE;AAEA,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKQ,kBAAsC;AAC5C,QAAI,OAAO,aAAa,YAAa,QAAO;AAE5C,WAAO,SAAS,OACb,MAAM,IAAI,EACV,KAAK,SAAO,IAAI,WAAW,GAAG,KAAK,UAAU,GAAG,CAAC,GAChD,MAAM,GAAG,EAAE,CAAC;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAyB;AACvB,QAAI,OAAO,aAAa,YAAa;AAGrC,aAAS,SAAS,GAAG,KAAK,UAAU;AAGpC,aAAS,SAAS;AAAA,EACpB;AACF;;;ACjKO,IAAM,gBAAN,MAAoB;AAAA;AAAA;AAAA;AAAA,EAIzB,OAAO,iBAAiB,MAAuB,cAA8C;AAC3F,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,QACL,SAAS;AAAA,QACT,eAAe;AAAA,QACf,QAAQ;AAAA,MACV;AAAA,IACF;AAEA,QAAI,CAAC,aAAa,SAAS,KAAK,IAAI,GAAG;AACrC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,aAAa,KAAK;AAAA,QAClB,eAAe;AAAA,QACf,QAAQ,cAAc,KAAK,IAAI,2BAA2B,aAAa,KAAK,IAAI,CAAC;AAAA,MACnF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,SAAS;AAAA,MACT,aAAa,KAAK;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,mBAAmB,MAA8D;AACtF,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,QACL,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,IACF;AAEA,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO;AAAA,QACL,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,IACF;AAEA,WAAO,EAAE,SAAS,KAAK;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,sBAAsB,aAAqB,cAAuC;AACvF,WAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS,mCAAmC,aAAa,KAAK,GAAG,CAAC,OAAO,WAAW;AAAA,IACtF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,gCAA+C;AACpD,WAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,4BAA2C;AAChD,WAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AACF;AAKO,IAAM,kBAAkB;AAAA;AAAA;AAAA;AAAA,EAI7B,SAAS,CAAC,SAAmC;AAC3C,WAAO,cAAc,iBAAiB,MAAM,CAAC,OAAO,CAAC,EAAE;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,CAAC,SAAmC;AAC7C,WAAO,cAAc,iBAAiB,MAAM,CAAC,SAAS,CAAC,EAAE;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,CAAC,SAAmC;AAChD,WAAO,cAAc,iBAAiB,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,CAAC,MAAuB,UAA6B;AAC/D,WAAO,cAAc,iBAAiB,MAAM,KAAK,EAAE;AAAA,EACrD;AACF;;;ACpHA,oBAA6B;AAOtB,SAAS,qBAAqB,QAA8B;AACjE,SAAO,eAAe,eAAe,SAAsB;AACzD,UAAM,EAAE,SAAS,IAAI,QAAQ;AAG7B,QAAI,OAAO,YAAY,KAAK,UAAQ,SAAS,WAAW,IAAI,CAAC,GAAG;AAC9D,aAAO,2BAAa,KAAK;AAAA,IAC3B;AAGA,UAAM,eAAe,QAAQ,QAAQ,IAAI,OAAO,UAAU;AAE1D,QAAI,CAAC,cAAc;AACjB,cAAQ,IAAI,mDAA8C;AAC1D,aAAO,2BAAa,SAAS,IAAI,IAAI,OAAO,WAAW,QAAQ,GAAG,CAAC;AAAA,IACrE;AAGA,QAAI;AACF,YAAM,WAAW,MAAM,MAAM,GAAG,OAAO,MAAM,aAAa;AAAA,QACxD,SAAS;AAAA,UACP,UAAU,GAAG,OAAO,UAAU,IAAI,aAAa,KAAK;AAAA,UACpD,gBAAgB;AAAA,QAClB;AAAA,MACF,CAAC;AAED,UAAI,SAAS,IAAI;AACf,cAAM,WAAW,MAAM,SAAS,KAAK;AACrC,cAAM,OAAO,SAAS,QAAQ;AAG9B,YAAI,QAAQ,CAAC,OAAO,aAAa,SAAS,KAAK,IAAI,GAAG;AACpD,kBAAQ,IAAI,uEAAgE;AAC5E,kBAAQ,IAAI,iBAAiB,KAAK,IAAI;AACtC,kBAAQ,IAAI,kBAAkB,OAAO,YAAY;AAGjD,gBAAMC,YAAW,2BAAa,SAAS,IAAI,IAAI,OAAO,WAAW,QAAQ,GAAG,CAAC;AAC7E,UAAAA,UAAS,QAAQ,OAAO,OAAO,UAAU;AACzC,iBAAOA;AAAA,QACT;AAGA,YAAI,QAAQ,CAAC,KAAK,UAAU;AAC1B,kBAAQ,IAAI,0EAAmE;AAG/E,gBAAMA,YAAW,2BAAa,SAAS,IAAI,IAAI,OAAO,WAAW,QAAQ,GAAG,CAAC;AAC7E,UAAAA,UAAS,QAAQ,OAAO,OAAO,UAAU;AACzC,iBAAOA;AAAA,QACT;AAEA,gBAAQ,IAAI,wDAAmD;AAC/D,eAAO,2BAAa,KAAK;AAAA,MAC3B,OAAO;AACL,gBAAQ,IAAI,sDAAiD;AAC7D,cAAMA,YAAW,2BAAa,SAAS,IAAI,IAAI,OAAO,WAAW,QAAQ,GAAG,CAAC;AAC7E,QAAAA,UAAS,QAAQ,OAAO,OAAO,UAAU;AACzC,eAAOA;AAAA,MACT;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,kCAA6B,KAAK;AAEhD,cAAQ,IAAI,qDAA2C;AACvD,aAAO,2BAAa,KAAK;AAAA,IAC3B;AAAA,EACF;AACF;AAKO,SAAS,0BAA0B,QAAgB;AACxD,SAAO,qBAAqB;AAAA,IAC1B;AAAA,IACA,cAAc,CAAC,OAAO;AAAA,IACtB,WAAW;AAAA,IACX,YAAY;AAAA;AAAA,IACZ,aAAa,CAAC,cAAc;AAAA,EAC9B,CAAC;AACH;AAKO,SAAS,4BAA4B,QAAgB;AAC1D,SAAO,qBAAqB;AAAA,IAC1B;AAAA,IACA,cAAc,CAAC,SAAS;AAAA,IACxB,WAAW;AAAA,IACX,YAAY;AAAA;AAAA,IACZ,aAAa,CAAC,UAAU,aAAa,SAAS;AAAA,EAChD,CAAC;AACH;AAKO,SAAS,+BAA+B,QAAgB;AAC7D,SAAO,qBAAqB;AAAA,IAC1B;AAAA,IACA,cAAc,CAAC,YAAY;AAAA,IAC3B,WAAW;AAAA,IACX,YAAY;AAAA;AAAA,IACZ,aAAa,CAAC,mBAAmB;AAAA,EACnC,CAAC;AACH;AAKO,SAAS,8BACd,QACA,cACA,WACA,YACA,cAAwB,CAAC,GACzB;AACA,SAAO,qBAAqB;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,CAAC,GAAG,aAAa,SAAS;AAAA,EACzC,CAAC;AACH;;;AChIO,IAAM,wBAAwC;AAAA,EACnD,oBAAoB;AAAA;AAAA,EACpB,oBAAoB;AAAA,EACpB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,oBAAoB;AAAA;AACtB;AAKO,IAAM,qBAAqC;AAAA,EAChD,oBAAoB;AAAA;AAAA,EACpB,oBAAoB;AAAA,EACpB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,oBAAoB;AAAA;AACtB;AAKO,IAAM,yBAAyC;AAAA,EACpD,oBAAoB;AAAA;AAAA,EACpB,oBAAoB;AAAA,EACpB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,oBAAoB;AAAA;AACtB;AAKO,SAAS,sBAAsB,QAA4B;AAChE,SAAO;AAAA,IACL;AAAA,IACA,cAAc,CAAC,OAAO;AAAA,IACtB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,YAAY;AAAA;AAAA,IACZ,gBAAgB;AAAA,EAClB;AACF;AAKO,SAAS,wBAAwB,QAA4B;AAClE,SAAO;AAAA,IACL;AAAA,IACA,cAAc,CAAC,SAAS;AAAA,IACxB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,YAAY;AAAA;AAAA,IACZ,gBAAgB;AAAA,EAClB;AACF;AAKO,SAAS,2BAA2B,QAA4B;AACrE,SAAO;AAAA,IACL;AAAA,IACA,cAAc,CAAC,YAAY;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,YAAY;AAAA;AAAA,IACZ,gBAAgB;AAAA,EAClB;AACF;AAKO,SAAS,0BACd,QACA,cACA,WACA,eACA,YACA,iBAAiC,uBACrB;AACZ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKO,IAAM,kBAAkB;AAKxB,IAAM,cAAc;AAAA,EACzB,OAAO,sBAAsB,eAAe;AAAA,EAC5C,SAAS,wBAAwB,eAAe;AAAA,EAChD,YAAY,2BAA2B,eAAe;AACxD;", "names": ["result", "response"]}