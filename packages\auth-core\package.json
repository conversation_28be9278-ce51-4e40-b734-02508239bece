{"name": "@grandline/auth-core", "version": "1.0.0", "description": "Shared authentication core logic for Grandline monorepo", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint src/", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "next": "^15.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": ">=18.0.0", "next": ">=14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "files": ["dist"], "publishConfig": {"access": "restricted"}}