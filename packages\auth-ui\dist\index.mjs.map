{"version": 3, "sources": ["../src/components/SecurityAlert.tsx"], "sourcesContent": ["import React from 'react';\nimport type { SecurityAlert as SecurityAlertType } from '@grandline/auth-core';\n\n// Icon components - these will be passed as props to make the component flexible\ninterface IconProps {\n  className?: string;\n}\n\ninterface SecurityAlertProps extends SecurityAlertType {\n  onClose?: () => void;\n  autoRedirect?: boolean;\n  redirectDelay?: number;\n  loginPath?: string; // Configurable login path\n  // Icon components passed as props for flexibility\n  ShieldIcon?: React.ComponentType<IconProps>;\n  AlertTriangleIcon?: React.ComponentType<IconProps>;\n  XIcon?: React.ComponentType<IconProps>;\n}\n\n/**\n * Security Alert Component\n * Shows critical security alerts when user access is revoked\n * Configurable for different apps with different login paths and icons\n */\nexport function SecurityAlert({ \n  type, \n  message, \n  onClose, \n  autoRedirect = true, \n  redirectDelay = 3000,\n  loginPath = '/login',\n  ShieldIcon,\n  AlertTriangleIcon,\n  XIcon\n}: SecurityAlertProps) {\n  const [countdown, setCountdown] = React.useState(Math.floor(redirectDelay / 1000));\n\n  React.useEffect(() => {\n    if (!autoRedirect) return;\n\n    const timer = setInterval(() => {\n      setCountdown((prev) => {\n        if (prev <= 1) {\n          clearInterval(timer);\n          window.location.href = loginPath;\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [autoRedirect, redirectDelay, loginPath]);\n\n  const getAlertConfig = () => {\n    switch (type) {\n      case 'role-changed':\n        return {\n          icon: ShieldIcon,\n          bgColor: 'bg-red-50',\n          borderColor: 'border-red-200',\n          iconColor: 'text-red-600',\n          titleColor: 'text-red-800',\n          messageColor: 'text-red-700',\n          title: 'Access Revoked - Role Changed',\n          buttonColor: 'bg-red-600 hover:bg-red-700',\n          progressColor: 'bg-red-500'\n        };\n      case 'account-deactivated':\n        return {\n          icon: AlertTriangleIcon,\n          bgColor: 'bg-orange-50',\n          borderColor: 'border-orange-200',\n          iconColor: 'text-orange-600',\n          titleColor: 'text-orange-800',\n          messageColor: 'text-orange-700',\n          title: 'Account Deactivated',\n          buttonColor: 'bg-orange-600 hover:bg-orange-700',\n          progressColor: 'bg-orange-500'\n        };\n      case 'session-expired':\n        return {\n          icon: ShieldIcon,\n          bgColor: 'bg-yellow-50',\n          borderColor: 'border-yellow-200',\n          iconColor: 'text-yellow-600',\n          titleColor: 'text-yellow-800',\n          messageColor: 'text-yellow-700',\n          title: 'Session Expired',\n          buttonColor: 'bg-yellow-600 hover:bg-yellow-700',\n          progressColor: 'bg-yellow-500'\n        };\n      default:\n        return {\n          icon: AlertTriangleIcon,\n          bgColor: 'bg-gray-50',\n          borderColor: 'border-gray-200',\n          iconColor: 'text-gray-600',\n          titleColor: 'text-gray-800',\n          messageColor: 'text-gray-700',\n          title: 'Security Alert',\n          buttonColor: 'bg-gray-600 hover:bg-gray-700',\n          progressColor: 'bg-gray-500'\n        };\n    }\n  };\n\n  const config = getAlertConfig();\n  const Icon = config.icon;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className={`max-w-md w-full mx-4 ${config.bgColor} ${config.borderColor} border rounded-lg shadow-lg`}>\n        <div className=\"p-6\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center\">\n              {Icon && (\n                <div className={`w-10 h-10 ${config.bgColor} rounded-full flex items-center justify-center mr-3`}>\n                  <Icon className={`w-6 h-6 ${config.iconColor}`} />\n                </div>\n              )}\n              <h3 className={`text-lg font-semibold ${config.titleColor}`}>\n                {config.title}\n              </h3>\n            </div>\n            {onClose && XIcon && (\n              <button\n                onClick={onClose}\n                className={`${config.iconColor} hover:opacity-75 transition-opacity`}\n              >\n                <XIcon className=\"w-5 h-5\" />\n              </button>\n            )}\n          </div>\n\n          {/* Message */}\n          <div className={`${config.messageColor} mb-4`}>\n            <p className=\"text-sm leading-relaxed\">{message}</p>\n          </div>\n\n          {/* Countdown */}\n          {autoRedirect && (\n            <div className={`${config.messageColor} text-center`}>\n              <p className=\"text-sm\">\n                Redirecting to login in <span className=\"font-semibold\">{countdown}</span> seconds...\n              </p>\n              <div className=\"mt-2 w-full bg-gray-200 rounded-full h-2\">\n                <div \n                  className={`h-2 rounded-full transition-all duration-1000 ${config.progressColor}`}\n                  style={{ width: `${(countdown / Math.floor(redirectDelay / 1000)) * 100}%` }}\n                />\n              </div>\n            </div>\n          )}\n\n          {/* Manual redirect button */}\n          <div className=\"mt-4 flex justify-center\">\n            <button\n              onClick={() => window.location.href = loginPath}\n              className={`px-4 py-2 text-sm font-medium text-white rounded-md transition-colors ${config.buttonColor}`}\n            >\n              Go to Login Now\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default SecurityAlert;\n"], "mappings": ";AAAA,OAAO,WAAW;AAwBX,SAAS,cAAc;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AACF,GAAuB;AACrB,QAAM,CAAC,WAAW,YAAY,IAAI,MAAM,SAAS,KAAK,MAAM,gBAAgB,GAAI,CAAC;AAEjF,QAAM,UAAU,MAAM;AACpB,QAAI,CAAC,aAAc;AAEnB,UAAM,QAAQ,YAAY,MAAM;AAC9B,mBAAa,CAAC,SAAS;AACrB,YAAI,QAAQ,GAAG;AACb,wBAAc,KAAK;AACnB,iBAAO,SAAS,OAAO;AACvB,iBAAO;AAAA,QACT;AACA,eAAO,OAAO;AAAA,MAChB,CAAC;AAAA,IACH,GAAG,GAAI;AAEP,WAAO,MAAM,cAAc,KAAK;AAAA,EAClC,GAAG,CAAC,cAAc,eAAe,SAAS,CAAC;AAE3C,QAAM,iBAAiB,MAAM;AAC3B,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,UACb,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,OAAO;AAAA,UACP,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,UACb,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,OAAO;AAAA,UACP,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,UACb,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,OAAO;AAAA,UACP,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,MACF;AACE,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,UACT,aAAa;AAAA,UACb,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,OAAO;AAAA,UACP,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,IACJ;AAAA,EACF;AAEA,QAAM,SAAS,eAAe;AAC9B,QAAM,OAAO,OAAO;AAEpB,SACE,oCAAC,SAAI,WAAU,gFACb,oCAAC,SAAI,WAAW,wBAAwB,OAAO,OAAO,IAAI,OAAO,WAAW,kCAC1E,oCAAC,SAAI,WAAU,SAEb,oCAAC,SAAI,WAAU,4CACb,oCAAC,SAAI,WAAU,uBACZ,QACC,oCAAC,SAAI,WAAW,aAAa,OAAO,OAAO,yDACzC,oCAAC,QAAK,WAAW,WAAW,OAAO,SAAS,IAAI,CAClD,GAEF,oCAAC,QAAG,WAAW,yBAAyB,OAAO,UAAU,MACtD,OAAO,KACV,CACF,GACC,WAAW,SACV;AAAA,IAAC;AAAA;AAAA,MACC,SAAS;AAAA,MACT,WAAW,GAAG,OAAO,SAAS;AAAA;AAAA,IAE9B,oCAAC,SAAM,WAAU,WAAU;AAAA,EAC7B,CAEJ,GAGA,oCAAC,SAAI,WAAW,GAAG,OAAO,YAAY,WACpC,oCAAC,OAAE,WAAU,6BAA2B,OAAQ,CAClD,GAGC,gBACC,oCAAC,SAAI,WAAW,GAAG,OAAO,YAAY,kBACpC,oCAAC,OAAE,WAAU,aAAU,4BACG,oCAAC,UAAK,WAAU,mBAAiB,SAAU,GAAO,aAC5E,GACA,oCAAC,SAAI,WAAU,8CACb;AAAA,IAAC;AAAA;AAAA,MACC,WAAW,iDAAiD,OAAO,aAAa;AAAA,MAChF,OAAO,EAAE,OAAO,GAAI,YAAY,KAAK,MAAM,gBAAgB,GAAI,IAAK,GAAG,IAAI;AAAA;AAAA,EAC7E,CACF,CACF,GAIF,oCAAC,SAAI,WAAU,8BACb;AAAA,IAAC;AAAA;AAAA,MACC,SAAS,MAAM,OAAO,SAAS,OAAO;AAAA,MACtC,WAAW,yEAAyE,OAAO,WAAW;AAAA;AAAA,IACvG;AAAA,EAED,CACF,CACF,CACF,CACF;AAEJ;AAEA,IAAO,wBAAQ;", "names": []}