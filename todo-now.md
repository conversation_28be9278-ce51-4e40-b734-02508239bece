Now, please implement an authentication for our trainee user role. Deeply analyze the apps/cms so you can understand better the real database user roles for trainees, and don't do cheap shallow implementation. Do it professionally and correctly because this is for enterprise level platform.
Please implement an authentication completely 100% now in our /signin

977-afe9e2a121019560.js:1 
 POST https://grandline-web.vercel.app/api/auth/login 405 (Method Not Allowed)

page-53c73614037aa860.js:1 🔘 BUTTON CLICKED! isSignUp: false isLoading: false
page-53c73614037aa860.js:1 🚀 FORM SUBMISSION STARTED
page-53c73614037aa860.js:1 📋 isSignUp: false
page-53c73614037aa860.js:1 📋 Form Data: 
{firstName: '', middleName: '', lastName: '', nameExtension: '', gender: '', …}
page-53c73614037aa860.js:1 📋 isLoading: false
977-afe9e2a121019560.js:1 
 POST https://grandline-web.vercel.app/api/auth/login 405 (Method Not Allowed)